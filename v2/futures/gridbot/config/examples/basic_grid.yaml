# Basic Grid Trading Configuration
# This is a simple configuration for beginners

# API Configuration
api:
  api_key: "your_binance_api_key_here"
  secret_key: "your_binance_secret_key_here"
  testnet: true  # Set to false for live trading
  # proxy: "http://127.0.0.1:7890"  # Optional proxy

# Trading Configuration
trading:
  symbol: "BTCUSDT"
  leverage: 5
  initial_margin: 100.0  # USDT
  dry_run: true  # Set to false for live trading

# Grid Strategy Configuration
grid:
  type: "neutral"  # neutral, long, short
  calculation: "arithmetic"  # arithmetic, geometric
  price_range:
    lower: 30000.0  # Lower bound of trading range
    upper: 35000.0  # Upper bound of trading range
  grid_count: 10  # Number of grid levels

# Risk Management Configuration
risk_management:
  # Stop Loss Configuration
  stop_loss:
    enabled: true
    price_trigger: 28000.0  # Stop if price goes below this level
    # pnl_trigger: -50.0  # Alternative: Stop if PnL drops below this amount

  # Take Profit Configuration
  take_profit:
    enabled: true
    roi_percentage: 10.0  # Take profit at 10% ROI
    # price_trigger: 40000.0  # Alternative: Take profit at specific price
    # pnl_trigger: 100.0  # Alternative: Take profit at specific PnL amount

  max_drawdown: 15.0  # Maximum allowed drawdown percentage
  margin_buffer: 25.0  # Keep 25% margin buffer to avoid liquidation

  # Emergency Stop Configuration
  emergency:
    enabled: true
    max_consecutive_loss: 5  # Stop after 5 consecutive losses
    liquidation_buffer: 10.0  # Emergency stop if within 10% of liquidation

# Monitoring Configuration
monitoring:
  log_level: "info"  # debug, info, warn, error, fatal
  metrics_enabled: true
  trade_history: true
  update_interval: 10s  # Status update interval
  # log_file: "gridbot.log"  # Optional log file
  # webhook_url: "https://hooks.slack.com/..."  # Optional webhook for notifications

# Advanced Configuration (Optional)
advanced:
  order_timeout: 30s
  max_retries: 3
  retry_delay: 5s
  price_deviation: 0.1  # Allow 0.1% price deviation
  min_order_size: 0.001  # Minimum order size
  rebalance_interval: 5m  # Grid rebalancing interval
  health_check_interval: 30s

  # WebSocket Configuration
  websocket:
    reconnect_interval: 5s
    ping_interval: 20s
    max_reconnects: 10
    buffer_size: 1000
