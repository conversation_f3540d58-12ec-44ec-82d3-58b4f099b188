package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/adshao/go-binance/v2/futures"
	"github.com/adshao/go-binance/v2/futures/gridbot/config"
	"github.com/adshao/go-binance/v2/futures/gridbot/market"
	"github.com/adshao/go-binance/v2/futures/gridbot/orders"
	"github.com/adshao/go-binance/v2/futures/gridbot/strategy"
)

func main() {
	// Load configuration
	cfg, err := config.LoadConfig("config/examples/basic_grid.yaml")
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Override with environment variables if available
	if apiKey := os.Getenv("BINANCE_API_KEY"); apiKey != "" {
		cfg.API.APIKey = apiKey
	}
	if secretKey := os.Getenv("BINANCE_SECRET_KEY"); secretKey != "" {
		cfg.API.SecretKey = secretKey
	}

	// Set testnet if specified
	if cfg.API.Testnet {
		futures.UseTestnet = true
	}

	// Create Binance futures client
	client := futures.NewClient(cfg.API.APIKey, cfg.API.SecretKey)

	// Create components
	marketHandler := market.NewMarketDataHandler(client, cfg, cfg.Trading.Symbol)
	orderManager := orders.NewOrderManager(client, cfg)
	gridStrategy := strategy.NewGridStrategy(cfg)

	// Create the main grid bot
	bot := &GridBot{
		config:        cfg,
		client:        client,
		marketHandler: marketHandler,
		orderManager:  orderManager,
		strategy:      gridStrategy,
		stopChan:      make(chan struct{}),
	}

	// Set up signal handling for graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Start the bot
	if err := bot.Start(); err != nil {
		log.Fatalf("Failed to start grid bot: %v", err)
	}

	fmt.Println("Grid trading bot started successfully!")
	fmt.Printf("Trading %s with %dx leverage\n", cfg.Trading.Symbol, cfg.Trading.Leverage)
	fmt.Printf("Grid range: %.2f - %.2f with %d levels\n", 
		cfg.Grid.PriceRange.Lower, cfg.Grid.PriceRange.Upper, cfg.Grid.GridCount)
	fmt.Printf("Initial margin: %.2f USDT\n", cfg.Trading.InitialMargin)
	
	if cfg.Trading.DryRun {
		fmt.Println("⚠️  Running in DRY RUN mode - no real trades will be executed")
	}

	// Run the bot
	go bot.Run()

	// Wait for shutdown signal
	<-sigChan
	fmt.Println("\nShutdown signal received, stopping grid bot...")

	// Stop the bot gracefully
	if err := bot.Stop(); err != nil {
		log.Printf("Error stopping bot: %v", err)
	}

	fmt.Println("Grid trading bot stopped successfully!")
}

// GridBot represents the main grid trading bot
type GridBot struct {
	config        *config.Config
	client        *futures.Client
	marketHandler market.MarketDataHandlerInterface
	orderManager  orders.OrderManagerInterface
	strategy      strategy.StrategyInterface
	
	running       bool
	stopChan      chan struct{}
}

// Start starts the grid bot
func (bot *GridBot) Start() error {
	// Start market data handler
	if err := bot.marketHandler.Start(); err != nil {
		return fmt.Errorf("failed to start market handler: %w", err)
	}

	// Subscribe to market data
	if err := bot.marketHandler.SubscribePriceUpdates(); err != nil {
		return fmt.Errorf("failed to subscribe to price updates: %w", err)
	}

	if err := bot.marketHandler.SubscribeDepthUpdates(); err != nil {
		return fmt.Errorf("failed to subscribe to depth updates: %w", err)
	}

	if err := bot.marketHandler.SubscribeUserDataUpdates(); err != nil {
		return fmt.Errorf("failed to subscribe to user data updates: %w", err)
	}

	// Start order manager
	if err := bot.orderManager.Start(); err != nil {
		return fmt.Errorf("failed to start order manager: %w", err)
	}

	// Initialize and start strategy
	if err := bot.strategy.Initialize(); err != nil {
		return fmt.Errorf("failed to initialize strategy: %w", err)
	}

	if err := bot.strategy.Start(); err != nil {
		return fmt.Errorf("failed to start strategy: %w", err)
	}

	bot.running = true
	return nil
}

// Stop stops the grid bot
func (bot *GridBot) Stop() error {
	if !bot.running {
		return nil
	}

	bot.running = false

	// Signal stop
	close(bot.stopChan)

	// Stop strategy
	if err := bot.strategy.Stop(); err != nil {
		log.Printf("Error stopping strategy: %v", err)
	}

	// Cancel all active orders
	if err := bot.orderManager.CancelAllOrders(bot.config.Trading.Symbol); err != nil {
		log.Printf("Error cancelling orders: %v", err)
	}

	// Stop order manager
	if err := bot.orderManager.Stop(); err != nil {
		log.Printf("Error stopping order manager: %v", err)
	}

	// Stop market handler
	if err := bot.marketHandler.Stop(); err != nil {
		log.Printf("Error stopping market handler: %v", err)
	}

	return nil
}

// Run runs the main bot loop
func (bot *GridBot) Run() {
	ticker := time.NewTicker(bot.config.Monitoring.UpdateInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			bot.update()
		case <-bot.stopChan:
			return
		}
	}
}

// update performs periodic updates
func (bot *GridBot) update() {
	// Get current market condition
	condition := bot.marketHandler.GetMarketCondition()
	if condition == nil {
		return
	}

	// Update strategy with market condition
	if err := bot.strategy.Update(condition); err != nil {
		log.Printf("Strategy update error: %v", err)
		return
	}

	// Print status periodically
	bot.printStatus(condition)
}

// printStatus prints the current bot status
func (bot *GridBot) printStatus(condition *market.MarketCondition) {
	stats := bot.strategy.GetStatistics()
	orderStats := bot.orderManager.GetStatistics()

	fmt.Printf("\n=== Grid Bot Status ===\n")
	fmt.Printf("Time: %s\n", time.Now().Format("2006-01-02 15:04:05"))
	fmt.Printf("Symbol: %s\n", bot.config.Trading.Symbol)
	fmt.Printf("Current Price: %.2f\n", condition.Price)
	fmt.Printf("Mark Price: %.2f\n", condition.MarkPrice)
	fmt.Printf("24h Change: %.2f%%\n", condition.PriceChange24h)
	fmt.Printf("Volatility: %.2f%%\n", condition.Volatility)
	fmt.Printf("Trend: %s\n", condition.Trend)
	
	if condition.BidPrice > 0 && condition.AskPrice > 0 {
		fmt.Printf("Bid/Ask: %.2f / %.2f (Spread: %.4f%%)\n", 
			condition.BidPrice, condition.AskPrice, condition.SpreadPercent)
	}

	fmt.Printf("\n--- Strategy Statistics ---\n")
	fmt.Printf("Status: %s\n", bot.strategy.GetStatus())
	fmt.Printf("Runtime: %s\n", stats.RunDuration)
	fmt.Printf("Total Trades: %d\n", stats.TotalTrades)
	fmt.Printf("Win Rate: %.2f%%\n", stats.WinRate)
	fmt.Printf("Total PnL: %.4f USDT\n", stats.TotalPnL)
	fmt.Printf("Net PnL: %.4f USDT\n", stats.NetPnL)
	fmt.Printf("ROI: %.2f%%\n", stats.ROI)
	fmt.Printf("Max Drawdown: %.2f%%\n", stats.MaxDrawdown)
	fmt.Printf("Grid Efficiency: %.2f%%\n", stats.GridEfficiency)

	fmt.Printf("\n--- Order Statistics ---\n")
	fmt.Printf("Active Orders: %d\n", orderStats.ActiveOrders)
	fmt.Printf("Total Orders: %d\n", orderStats.TotalOrders)
	fmt.Printf("Filled Orders: %d\n", orderStats.FilledOrders)
	fmt.Printf("Success Rate: %.2f%%\n", orderStats.SuccessRate)
	fmt.Printf("Total Volume: %.4f\n", orderStats.TotalVolume)

	// Show grid levels status
	levels := bot.strategy.GetGridLevels()
	activeLevels := 0
	filledLevels := 0
	for _, level := range levels {
		switch level.Status {
		case config.GridLevelStatusActive:
			activeLevels++
		case config.GridLevelStatusFilled:
			filledLevels++
		}
	}
	fmt.Printf("Grid Levels: %d total, %d active, %d filled\n", 
		len(levels), activeLevels, filledLevels)

	fmt.Printf("========================\n")
}
