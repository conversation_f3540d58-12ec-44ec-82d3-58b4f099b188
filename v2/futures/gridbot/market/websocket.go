package market

import (
	"strconv"
	"time"

	"github.com/adshao/go-binance/v2/futures"
)

// WebSocket event handlers

// handleMarkPriceUpdate handles mark price updates from WebSocket
func (mdh *MarketDataHandlerImpl) handleMarkPriceUpdate(event *futures.WsMarkPriceEvent) {
	mdh.mu.Lock()
	defer mdh.mu.Unlock()
	
	price, _ := strconv.ParseFloat(event.MarkPrice, 64)
	indexPrice, _ := strconv.ParseFloat(event.IndexPrice, 64)
	
	// Update current price data
	if mdh.currentPrice == nil {
		mdh.currentPrice = &PriceData{}
	}
	
	mdh.currentPrice.Symbol = event.Symbol
	mdh.currentPrice.MarkPrice = price
	mdh.currentPrice.IndexPrice = indexPrice
	mdh.currentPrice.Price = price // Use mark price as main price
	mdh.currentPrice.Timestamp = time.Unix(0, event.Time*int64(time.Millisecond))
	
	// Add to price history
	mdh.addPriceToHistory(price)
	
	// Update statistics
	mdh.statistics.TotalPriceUpdates++
	
	// Send price update
	update := &PriceUpdate{
		Symbol:    event.Symbol,
		Price:     price,
		Timestamp: mdh.currentPrice.Timestamp,
		Source:    "markPrice",
	}
	
	select {
	case mdh.priceUpdates <- update:
		// Update sent successfully
	default:
		// Channel is full, skip update (non-blocking)
	}
	
	// Update connection ping time
	if conn, exists := mdh.connections["markPrice"]; exists {
		conn.lastPing = time.Now()
	}
}

// handleTickerUpdate handles ticker updates from WebSocket
func (mdh *MarketDataHandlerImpl) handleTickerUpdate(event *futures.WsMarketTickerEvent) {
	mdh.mu.Lock()
	defer mdh.mu.Unlock()
	
	price, _ := strconv.ParseFloat(event.LastPrice, 64)
	bidPrice, _ := strconv.ParseFloat(event.BidPrice, 64)
	askPrice, _ := strconv.ParseFloat(event.AskPrice, 64)
	volume, _ := strconv.ParseFloat(event.BaseVolume, 64)
	change, _ := strconv.ParseFloat(event.PriceChange, 64)
	changePercent, _ := strconv.ParseFloat(event.PriceChangePercent, 64)
	high, _ := strconv.ParseFloat(event.HighPrice, 64)
	low, _ := strconv.ParseFloat(event.LowPrice, 64)
	
	// Update current price data
	if mdh.currentPrice == nil {
		mdh.currentPrice = &PriceData{}
	}
	
	mdh.currentPrice.Symbol = event.Symbol
	mdh.currentPrice.LastPrice = price
	mdh.currentPrice.BidPrice = bidPrice
	mdh.currentPrice.AskPrice = askPrice
	mdh.currentPrice.Volume24h = volume
	mdh.currentPrice.Change24h = change
	mdh.currentPrice.ChangePercent24h = changePercent
	mdh.currentPrice.High24h = high
	mdh.currentPrice.Low24h = low
	mdh.currentPrice.Timestamp = time.Unix(0, event.Time*int64(time.Millisecond))
	
	// Use last price as main price if mark price is not available
	if mdh.currentPrice.Price == 0 {
		mdh.currentPrice.Price = price
		mdh.addPriceToHistory(price)
	}
	
	// Update statistics
	mdh.statistics.TotalPriceUpdates++
	
	// Send price update
	update := &PriceUpdate{
		Symbol:    event.Symbol,
		Price:     price,
		Timestamp: mdh.currentPrice.Timestamp,
		Source:    "ticker",
	}
	
	select {
	case mdh.priceUpdates <- update:
		// Update sent successfully
	default:
		// Channel is full, skip update (non-blocking)
	}
	
	// Update connection ping time
	if conn, exists := mdh.connections["ticker"]; exists {
		conn.lastPing = time.Now()
	}
}

// handleDepthUpdate handles order book depth updates from WebSocket
func (mdh *MarketDataHandlerImpl) handleDepthUpdate(event *futures.WsDepthEvent) {
	mdh.mu.Lock()
	defer mdh.mu.Unlock()
	
	// Convert bids and asks
	bids := make([]PriceLevel, len(event.Bids))
	for i, bid := range event.Bids {
		price, _ := strconv.ParseFloat(bid.Price, 64)
		quantity, _ := strconv.ParseFloat(bid.Quantity, 64)
		bids[i] = PriceLevel{
			Price:    price,
			Quantity: quantity,
		}
	}
	
	asks := make([]PriceLevel, len(event.Asks))
	for i, ask := range event.Asks {
		price, _ := strconv.ParseFloat(ask.Price, 64)
		quantity, _ := strconv.ParseFloat(ask.Quantity, 64)
		asks[i] = PriceLevel{
			Price:    price,
			Quantity: quantity,
		}
	}
	
	// Update current depth data
	mdh.currentDepth = &DepthData{
		Symbol:       event.Symbol,
		Bids:         bids,
		Asks:         asks,
		LastUpdateID: event.LastUpdateID,
		Timestamp:    time.Unix(0, event.Time*int64(time.Millisecond)),
	}
	
	// Update statistics
	mdh.statistics.TotalDepthUpdates++
	
	// Send depth update
	update := &DepthUpdate{
		Symbol:    event.Symbol,
		Bids:      bids,
		Asks:      asks,
		Timestamp: mdh.currentDepth.Timestamp,
		UpdateID:  event.LastUpdateID,
	}
	
	select {
	case mdh.depthUpdates <- update:
		// Update sent successfully
	default:
		// Channel is full, skip update (non-blocking)
	}
	
	// Update connection ping time
	if conn, exists := mdh.connections["depth"]; exists {
		conn.lastPing = time.Now()
	}
}

// handleUserDataUpdate handles user data updates from WebSocket
func (mdh *MarketDataHandlerImpl) handleUserDataUpdate(event *futures.WsUserDataEvent) {
	mdh.mu.Lock()
	defer mdh.mu.Unlock()
	
	var update *UserDataUpdate
	
	switch event.Event {
	case futures.UserDataEventTypeOrderTradeUpdate:
		// Handle order update
		orderData := &OrderUpdateData{
			Symbol:           event.OrderTradeUpdate.Symbol,
			OrderID:          event.OrderTradeUpdate.ID,
			ClientOrderID:    event.OrderTradeUpdate.ClientOrderID,
			Side:             event.OrderTradeUpdate.Side,
			Type:             event.OrderTradeUpdate.Type,
			Status:           event.OrderTradeUpdate.Status,
			Quantity:         event.OrderTradeUpdate.OriginalQty,
			Price:            event.OrderTradeUpdate.Price,
			ExecutedQuantity: event.OrderTradeUpdate.AccumulatedFilledQty,
			CumulativeQuote:  event.OrderTradeUpdate.CumulativeQuoteQty,
			Commission:       event.OrderTradeUpdate.Commission,
			CommissionAsset:  event.OrderTradeUpdate.CommissionAsset,
			TradeTime:        time.Unix(0, event.OrderTradeUpdate.TradeTime*int64(time.Millisecond)),
		}
		
		update = &UserDataUpdate{
			Type:      UserDataTypeOrderUpdate,
			Timestamp: time.Unix(0, event.Time*int64(time.Millisecond)),
			Data:      orderData,
		}
		
	case futures.UserDataEventTypeAccountUpdate:
		// Handle account update
		accountData := &AccountUpdateData{
			TotalWalletBalance:          event.AccountUpdate.TotalWalletBalance,
			TotalUnrealizedPnL:          event.AccountUpdate.TotalUnrealizedPnL,
			TotalMarginBalance:          event.AccountUpdate.TotalMarginBalance,
			TotalInitialMargin:          event.AccountUpdate.TotalInitialMargin,
			TotalMaintMargin:            event.AccountUpdate.TotalMaintMargin,
			TotalPositionInitialMargin:  event.AccountUpdate.TotalPositionInitialMargin,
			TotalOpenOrderInitialMargin: event.AccountUpdate.TotalOpenOrderInitialMargin,
			MaxWithdrawAmount:           event.AccountUpdate.MaxWithdrawAmount,
		}
		
		update = &UserDataUpdate{
			Type:      UserDataTypeAccountUpdate,
			Timestamp: time.Unix(0, event.Time*int64(time.Millisecond)),
			Data:      accountData,
		}
		
		// Also handle position updates if present
		if len(event.AccountUpdate.Positions) > 0 {
			for _, pos := range event.AccountUpdate.Positions {
				positionData := &PositionUpdateData{
					Symbol:         pos.Symbol,
					PositionAmount: pos.PositionAmt,
					EntryPrice:     pos.EntryPrice,
					MarkPrice:      pos.MarkPrice,
					UnrealizedPnL:  pos.UnrealizedPnL,
					MarginType:     pos.MarginType,
					IsolatedWallet: pos.IsolatedWallet,
					PositionSide:   pos.PositionSide,
				}
				
				posUpdate := &UserDataUpdate{
					Type:      UserDataTypePositionUpdate,
					Timestamp: time.Unix(0, event.Time*int64(time.Millisecond)),
					Data:      positionData,
				}
				
				select {
				case mdh.userUpdates <- posUpdate:
					// Update sent successfully
				default:
					// Channel is full, skip update (non-blocking)
				}
			}
		}
		
	default:
		// Unknown event type, create generic update
		update = &UserDataUpdate{
			Type:      UserDataType("unknown"),
			Timestamp: time.Unix(0, event.Time*int64(time.Millisecond)),
			Data:      event,
		}
	}
	
	// Update statistics
	mdh.statistics.TotalUserUpdates++
	
	// Send user data update
	if update != nil {
		select {
		case mdh.userUpdates <- update:
			// Update sent successfully
		default:
			// Channel is full, skip update (non-blocking)
		}
	}
	
	// Update connection ping time
	if conn, exists := mdh.connections["userData"]; exists {
		conn.lastPing = time.Now()
	}
}

// handleError handles WebSocket errors
func (mdh *MarketDataHandlerImpl) handleError(err error) {
	mdh.mu.Lock()
	defer mdh.mu.Unlock()
	
	mdh.statistics.TotalErrors++
	mdh.statistics.ConnectionErrors++
	now := time.Now()
	mdh.statistics.LastErrorTime = &now
	mdh.statistics.LastError = err.Error()
	
	mdh.sendEvent(MarketDataEventError, nil, err.Error())
	
	// Mark connections as disconnected
	for _, conn := range mdh.connections {
		conn.connected = false
	}
	mdh.statistics.ActiveConnections = 0
	
	// Attempt reconnection after a delay
	go func() {
		time.Sleep(mdh.config.Advanced.WebSocket.ReconnectInterval)
		if mdh.IsRunning() {
			mdh.Reconnect()
		}
	}()
}
