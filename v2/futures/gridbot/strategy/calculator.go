package strategy

import (
	"fmt"
	"math"
	"time"

	"github.com/adshao/go-binance/v2/futures"
	"github.com/adshao/go-binance/v2/futures/gridbot/config"
)

// GridCalculator handles grid price calculations
type GridCalculator struct {
	config *config.Config
}

// NewGridCalculator creates a new grid calculator
func NewGridCalculator(cfg *config.Config) *GridCalculator {
	return &GridCalculator{
		config: cfg,
	}
}

// CalculateGridLevels calculates all grid levels based on configuration
func (gc *GridCalculator) CalculateGridLevels() (*GridCalculationResult, error) {
	if len(gc.config.Grid.CustomLevels) > 0 {
		return gc.calculateCustomGridLevels()
	}

	switch gc.config.Grid.Calculation {
	case config.CalculationArithmetic:
		return gc.calculateArithmeticGrid()
	case config.CalculationGeometric:
		return gc.calculateGeometricGrid()
	default:
		return nil, fmt.Errorf("unsupported calculation type: %s", gc.config.Grid.Calculation)
	}
}

// calculateArithmeticGrid calculates grid levels with equal price differences
func (gc *GridCalculator) calculateArithmeticGrid() (*GridCalculationResult, error) {
	lower := gc.config.Grid.PriceRange.Lower
	upper := gc.config.Grid.PriceRange.Upper
	gridCount := gc.config.Grid.GridCount

	// Calculate grid spacing
	priceRange := upper - lower
	gridSpacing := priceRange / float64(gridCount-1)

	levels := make([]*GridLevel, 0, gridCount)
	totalQuantity := 0.0

	for i := 0; i < gridCount; i++ {
		price := lower + float64(i)*gridSpacing
		
		// Determine side and quantity based on grid type
		side, quantity := gc.determineSideAndQuantity(price, lower, upper)
		
		level := &GridLevel{
			ID:        i,
			Price:     price,
			Side:      side,
			Quantity:  quantity,
			Status:    config.GridLevelStatusPending,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		
		levels = append(levels, level)
		totalQuantity += quantity
	}

	requiredMargin := gc.calculateRequiredMargin(totalQuantity)

	return &GridCalculationResult{
		Levels:         levels,
		TotalQuantity:  totalQuantity,
		RequiredMargin: requiredMargin,
		GridSpacing:    gridSpacing,
		PriceRange:     priceRange,
	}, nil
}

// calculateGeometricGrid calculates grid levels with equal percentage differences
func (gc *GridCalculator) calculateGeometricGrid() (*GridCalculationResult, error) {
	lower := gc.config.Grid.PriceRange.Lower
	upper := gc.config.Grid.PriceRange.Upper
	gridCount := gc.config.Grid.GridCount

	// Calculate geometric ratio
	ratio := math.Pow(upper/lower, 1.0/float64(gridCount-1))
	
	levels := make([]*GridLevel, 0, gridCount)
	totalQuantity := 0.0

	for i := 0; i < gridCount; i++ {
		price := lower * math.Pow(ratio, float64(i))
		
		// Determine side and quantity based on grid type
		side, quantity := gc.determineSideAndQuantity(price, lower, upper)
		
		level := &GridLevel{
			ID:        i,
			Price:     price,
			Side:      side,
			Quantity:  quantity,
			Status:    config.GridLevelStatusPending,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		
		levels = append(levels, level)
		totalQuantity += quantity
	}

	requiredMargin := gc.calculateRequiredMargin(totalQuantity)
	avgSpacing := (upper - lower) / float64(gridCount-1)

	return &GridCalculationResult{
		Levels:         levels,
		TotalQuantity:  totalQuantity,
		RequiredMargin: requiredMargin,
		GridSpacing:    avgSpacing,
		PriceRange:     upper - lower,
	}, nil
}

// calculateCustomGridLevels uses custom price levels from configuration
func (gc *GridCalculator) calculateCustomGridLevels() (*GridCalculationResult, error) {
	customLevels := gc.config.Grid.CustomLevels
	lower := gc.config.Grid.PriceRange.Lower
	upper := gc.config.Grid.PriceRange.Upper

	levels := make([]*GridLevel, 0, len(customLevels))
	totalQuantity := 0.0

	for i, price := range customLevels {
		// Determine side and quantity based on grid type
		side, quantity := gc.determineSideAndQuantity(price, lower, upper)
		
		level := &GridLevel{
			ID:        i,
			Price:     price,
			Side:      side,
			Quantity:  quantity,
			Status:    config.GridLevelStatusPending,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		
		levels = append(levels, level)
		totalQuantity += quantity
	}

	requiredMargin := gc.calculateRequiredMargin(totalQuantity)
	avgSpacing := (upper - lower) / float64(len(customLevels)-1)

	return &GridCalculationResult{
		Levels:         levels,
		TotalQuantity:  totalQuantity,
		RequiredMargin: requiredMargin,
		GridSpacing:    avgSpacing,
		PriceRange:     upper - lower,
	}, nil
}

// determineSideAndQuantity determines order side and quantity based on grid type and price position
func (gc *GridCalculator) determineSideAndQuantity(price, lower, upper float64) (futures.SideType, float64) {
	// Calculate base quantity from initial margin
	baseQuantity := gc.calculateBaseQuantity(price)
	
	// Apply minimum order size constraint
	if baseQuantity < gc.config.Advanced.MinOrderSize {
		baseQuantity = gc.config.Advanced.MinOrderSize
	}
	
	// Apply maximum order size constraint if set
	if gc.config.Advanced.MaxOrderSize > 0 && baseQuantity > gc.config.Advanced.MaxOrderSize {
		baseQuantity = gc.config.Advanced.MaxOrderSize
	}

	switch gc.config.Grid.Type {
	case config.GridTypeNeutral:
		// For neutral grid, alternate buy/sell orders
		// Place buy orders in lower half, sell orders in upper half
		midPrice := (lower + upper) / 2
		if price <= midPrice {
			return futures.SideTypeBuy, baseQuantity
		}
		return futures.SideTypeSell, baseQuantity

	case config.GridTypeLong:
		// For long grid, primarily buy orders with some sell orders for profit taking
		// Place more buy orders at lower prices
		priceRatio := (price - lower) / (upper - lower)
		if priceRatio < 0.7 { // Buy in lower 70% of range
			return futures.SideTypeBuy, baseQuantity
		}
		return futures.SideTypeSell, baseQuantity * 0.8 // Smaller sell orders

	case config.GridTypeShort:
		// For short grid, primarily sell orders with some buy orders for covering
		// Place more sell orders at higher prices
		priceRatio := (price - lower) / (upper - lower)
		if priceRatio > 0.3 { // Sell in upper 70% of range
			return futures.SideTypeSell, baseQuantity
		}
		return futures.SideTypeBuy, baseQuantity * 0.8 // Smaller buy orders

	default:
		// Default to neutral behavior
		midPrice := (lower + upper) / 2
		if price <= midPrice {
			return futures.SideTypeBuy, baseQuantity
		}
		return futures.SideTypeSell, baseQuantity
	}
}

// calculateBaseQuantity calculates the base quantity for orders
func (gc *GridCalculator) calculateBaseQuantity(price float64) float64 {
	// Calculate quantity based on initial margin and leverage
	leverage := float64(gc.config.Trading.Leverage)
	initialMargin := gc.config.Trading.InitialMargin
	gridCount := float64(gc.config.Grid.GridCount)
	
	// Distribute margin across all grid levels
	marginPerGrid := initialMargin / gridCount
	
	// Calculate quantity: (margin * leverage) / price
	quantity := (marginPerGrid * leverage) / price
	
	return quantity
}

// calculateRequiredMargin calculates the total margin required for all orders
func (gc *GridCalculator) calculateRequiredMargin(totalQuantity float64) float64 {
	// For futures, margin = (quantity * price) / leverage
	// We'll use average price for estimation
	avgPrice := (gc.config.Grid.PriceRange.Lower + gc.config.Grid.PriceRange.Upper) / 2
	leverage := float64(gc.config.Trading.Leverage)
	
	requiredMargin := (totalQuantity * avgPrice) / leverage
	
	// Add margin buffer
	bufferMultiplier := 1.0 + (gc.config.Risk.MarginBuffer / 100.0)
	
	return requiredMargin * bufferMultiplier
}

// CalculateOptimalGridCount calculates optimal grid count based on price range and volatility
func (gc *GridCalculator) CalculateOptimalGridCount(volatility float64) int {
	lower := gc.config.Grid.PriceRange.Lower
	upper := gc.config.Grid.PriceRange.Upper
	priceRange := upper - lower
	avgPrice := (lower + upper) / 2
	
	// Calculate price range as percentage
	rangePercent := (priceRange / avgPrice) * 100
	
	// Base grid count on range and volatility
	// More volatile markets need fewer grids to avoid over-trading
	// Wider ranges can accommodate more grids
	baseGrids := int(rangePercent / 2) // 2% per grid as base
	
	// Adjust for volatility
	volatilityAdjustment := 1.0 - (volatility / 100.0) // Reduce grids for high volatility
	adjustedGrids := int(float64(baseGrids) * volatilityAdjustment)
	
	// Ensure within reasonable bounds
	if adjustedGrids < 5 {
		adjustedGrids = 5
	}
	if adjustedGrids > 50 {
		adjustedGrids = 50
	}
	
	return adjustedGrids
}

// CalculateGridSpacing calculates the spacing between grid levels
func (gc *GridCalculator) CalculateGridSpacing() float64 {
	lower := gc.config.Grid.PriceRange.Lower
	upper := gc.config.Grid.PriceRange.Upper
	gridCount := gc.config.Grid.GridCount
	
	switch gc.config.Grid.Calculation {
	case config.CalculationArithmetic:
		return (upper - lower) / float64(gridCount-1)
	case config.CalculationGeometric:
		ratio := math.Pow(upper/lower, 1.0/float64(gridCount-1))
		return lower * (ratio - 1) // Average spacing for geometric
	default:
		return (upper - lower) / float64(gridCount-1)
	}
}

// ValidateGridConfiguration validates the grid configuration
func (gc *GridCalculator) ValidateGridConfiguration() error {
	// Check if price range is valid
	if gc.config.Grid.PriceRange.Lower >= gc.config.Grid.PriceRange.Upper {
		return fmt.Errorf("invalid price range: lower (%f) must be less than upper (%f)",
			gc.config.Grid.PriceRange.Lower, gc.config.Grid.PriceRange.Upper)
	}
	
	// Check if grid count is reasonable
	if gc.config.Grid.GridCount < 2 {
		return fmt.Errorf("grid count must be at least 2, got %d", gc.config.Grid.GridCount)
	}
	
	// Check if initial margin is sufficient
	result, err := gc.CalculateGridLevels()
	if err != nil {
		return fmt.Errorf("failed to calculate grid levels: %w", err)
	}
	
	if result.RequiredMargin > gc.config.Trading.InitialMargin {
		return fmt.Errorf("insufficient initial margin: required %f, available %f",
			result.RequiredMargin, gc.config.Trading.InitialMargin)
	}
	
	return nil
}
