package strategy

import (
	"fmt"
	"math"
	"sync"
	"time"

	"github.com/adshao/go-binance/v2/futures"
	"github.com/adshao/go-binance/v2/futures/gridbot/config"
)

// GridStrategyImpl implements the main grid trading strategy
type GridStrategyImpl struct {
	*GridStrategy // Embed the base struct from types.go

	calculator *GridCalculator

	// Synchronization
	mu sync.RWMutex

	// Channels for communication
	eventChan   chan *GridEvent
	stopChan    chan struct{}

	// Current market condition
	currentCondition *MarketCondition

	// Order management
	activeOrders map[int64]*GridLevel
	orderHistory []*GridLevel
}

// NewGridStrategy creates a new grid trading strategy
func NewGridStrategy(cfg *config.Config) *GridStrategyImpl {
	now := time.Now()

	baseStrategy := &GridStrategy{
		Config:     cfg,
		Levels:     make([]*GridLevel, 0),
		Statistics: &StrategyStatistics{},
		Status:     StrategyStatusInitializing,
		CreatedAt:  now,
		UpdatedAt:  now,
	}

	return &GridStrategyImpl{
		GridStrategy:     baseStrategy,
		calculator:       NewGridCalculator(cfg),
		eventChan:        make(chan *GridEvent, 100),
		stopChan:         make(chan struct{}),
		activeOrders:     make(map[int64]*GridLevel),
		orderHistory:     make([]*GridLevel, 0),
	}
}

// Initialize initializes the grid strategy
func (gs *GridStrategyImpl) Initialize() error {
	gs.mu.Lock()
	defer gs.mu.Unlock()
	
	// Validate configuration
	if err := gs.calculator.ValidateGridConfiguration(); err != nil {
		return fmt.Errorf("configuration validation failed: %w", err)
	}
	
	// Calculate grid levels
	result, err := gs.calculator.CalculateGridLevels()
	if err != nil {
		return fmt.Errorf("failed to calculate grid levels: %w", err)
	}
	
	gs.Levels = result.Levels

	// Initialize statistics
	gs.Statistics = &StrategyStatistics{
		StartTime:      time.Now(),
		ActiveGrids:    len(gs.Levels),
		TotalTrades:    0,
		TotalPnL:       0,
		NetPnL:         0,
		MaxDrawdown:    0,
		CurrentDrawdown: 0,
		WinRate:        0,
		GridEfficiency: 0,
	}

	gs.Status = StrategyStatusActive
	gs.UpdatedAt = time.Now()
	
	// Send initialization event
	gs.sendEvent(GridEventType("strategy_initialized"), nil, 0, "Grid strategy initialized successfully")
	
	return nil
}

// Start starts the grid strategy
func (gs *GridStrategy) Start() error {
	gs.mu.Lock()
	defer gs.mu.Unlock()
	
	if gs.status != StrategyStatusActive && gs.status != StrategyStatusPaused {
		return fmt.Errorf("cannot start strategy in status: %s", gs.status)
	}
	
	now := time.Now()
	gs.startedAt = &now
	gs.status = StrategyStatusActive
	gs.statistics.StartTime = now
	
	// Send start event
	gs.sendEvent(GridEventType("strategy_started"), nil, 0, "Grid strategy started")
	
	return nil
}

// Stop stops the grid strategy
func (gs *GridStrategy) Stop() error {
	gs.mu.Lock()
	defer gs.mu.Unlock()
	
	if gs.status == StrategyStatusStopped {
		return nil // Already stopped
	}
	
	now := time.Now()
	gs.stoppedAt = &now
	gs.status = StrategyStatusStopped
	
	if gs.statistics.EndTime == nil {
		gs.statistics.EndTime = &now
		gs.statistics.RunDuration = now.Sub(gs.statistics.StartTime)
	}
	
	// Close stop channel to signal shutdown
	select {
	case <-gs.stopChan:
		// Already closed
	default:
		close(gs.stopChan)
	}
	
	// Send stop event
	gs.sendEvent(GridEventType("strategy_stopped"), nil, 0, "Grid strategy stopped")
	
	return nil
}

// Pause pauses the grid strategy
func (gs *GridStrategy) Pause() error {
	gs.mu.Lock()
	defer gs.mu.Unlock()
	
	if gs.status != StrategyStatusActive {
		return fmt.Errorf("cannot pause strategy in status: %s", gs.status)
	}
	
	gs.status = StrategyStatusPaused
	gs.updatedAt = time.Now()
	
	// Send pause event
	gs.sendEvent(GridEventStrategyPaused, nil, 0, "Grid strategy paused")
	
	return nil
}

// Resume resumes the grid strategy
func (gs *GridStrategy) Resume() error {
	gs.mu.Lock()
	defer gs.mu.Unlock()
	
	if gs.status != StrategyStatusPaused {
		return fmt.Errorf("cannot resume strategy in status: %s", gs.status)
	}
	
	gs.status = StrategyStatusActive
	gs.updatedAt = time.Now()
	
	// Send resume event
	gs.sendEvent(GridEventStrategyResumed, nil, 0, "Grid strategy resumed")
	
	return nil
}

// Update updates the strategy with new market data
func (gs *GridStrategy) Update(condition *MarketCondition) error {
	gs.mu.Lock()
	defer gs.mu.Unlock()
	
	if gs.status != StrategyStatusActive {
		return nil // Skip updates when not active
	}
	
	gs.currentCondition = condition
	gs.updatedAt = time.Now()
	
	// Update statistics with current price
	gs.updatePriceStatistics(condition.Price)
	
	// Check if price is within grid range
	if !gs.isPriceInRange(condition.Price) {
		gs.sendEvent(GridEventPriceOutOfRange, nil, condition.Price, 
			fmt.Sprintf("Price %.2f is outside grid range [%.2f, %.2f]", 
				condition.Price, gs.config.Grid.PriceRange.Lower, gs.config.Grid.PriceRange.Upper))
		return nil
	}
	
	// Check for risk triggers
	if err := gs.checkRiskTriggers(condition); err != nil {
		gs.sendEvent(GridEventRiskTriggered, nil, condition.Price, err.Error())
		return err
	}
	
	return nil
}

// GetStatus returns the current strategy status
func (gs *GridStrategy) GetStatus() StrategyStatus {
	gs.mu.RLock()
	defer gs.mu.RUnlock()
	return gs.status
}

// GetStatistics returns the current strategy statistics
func (gs *GridStrategy) GetStatistics() *StrategyStatistics {
	gs.mu.RLock()
	defer gs.mu.RUnlock()
	
	// Update runtime statistics
	stats := *gs.statistics
	if gs.startedAt != nil {
		if gs.stoppedAt != nil {
			stats.RunDuration = gs.stoppedAt.Sub(*gs.startedAt)
		} else {
			stats.RunDuration = time.Since(*gs.startedAt)
		}
	}
	
	// Calculate derived statistics
	if stats.TotalTrades > 0 {
		stats.WinRate = float64(stats.ProfitableTrades) / float64(stats.TotalTrades) * 100
		stats.AvgTradeSize = stats.TotalVolume / float64(stats.TotalTrades)
		
		if stats.RunDuration > 0 {
			hours := stats.RunDuration.Hours()
			stats.TradesPerHour = float64(stats.TotalTrades) / hours
			stats.VolumePerHour = stats.TotalVolume / hours
		}
	}
	
	// Calculate ROI
	if gs.config.Trading.InitialMargin > 0 {
		stats.ROI = (stats.NetPnL / gs.config.Trading.InitialMargin) * 100
	}
	
	// Calculate grid efficiency
	if len(gs.levels) > 0 {
		stats.GridEfficiency = float64(stats.FilledGrids) / float64(len(gs.levels)) * 100
	}
	
	return &stats
}

// GetGridLevels returns the current grid levels
func (gs *GridStrategy) GetGridLevels() []*GridLevel {
	gs.mu.RLock()
	defer gs.mu.RUnlock()
	
	// Return a copy to prevent external modification
	levels := make([]*GridLevel, len(gs.levels))
	copy(levels, gs.levels)
	return levels
}

// Validate validates the strategy configuration
func (gs *GridStrategy) Validate() error {
	return gs.calculator.ValidateGridConfiguration()
}

// Helper methods

// sendEvent sends an event to the event channel
func (gs *GridStrategy) sendEvent(eventType GridEventType, level *GridLevel, price float64, message string) {
	event := &GridEvent{
		Type:      eventType,
		Timestamp: time.Now(),
		GridLevel: level,
		Price:     price,
		Message:   message,
	}
	
	select {
	case gs.eventChan <- event:
		// Event sent successfully
	default:
		// Channel is full, skip event (non-blocking)
	}
}

// isPriceInRange checks if the price is within the grid range
func (gs *GridStrategy) isPriceInRange(price float64) bool {
	return price >= gs.config.Grid.PriceRange.Lower && price <= gs.config.Grid.PriceRange.Upper
}

// updatePriceStatistics updates price-related statistics
func (gs *GridStrategy) updatePriceStatistics(price float64) {
	if gs.statistics.HighestPrice == 0 || price > gs.statistics.HighestPrice {
		gs.statistics.HighestPrice = price
	}
	
	if gs.statistics.LowestPrice == 0 || price < gs.statistics.LowestPrice {
		gs.statistics.LowestPrice = price
	}
	
	gs.statistics.CurrentPrice = price
	gs.statistics.PriceRange = gs.statistics.HighestPrice - gs.statistics.LowestPrice
}

// checkRiskTriggers checks if any risk management triggers are activated
func (gs *GridStrategy) checkRiskTriggers(condition *MarketCondition) error {
	// Check stop loss triggers
	if gs.config.Risk.StopLoss.Enabled {
		if gs.config.Risk.StopLoss.PriceTrigger > 0 && condition.Price <= gs.config.Risk.StopLoss.PriceTrigger {
			return fmt.Errorf("stop loss price trigger activated: price %.2f <= trigger %.2f", 
				condition.Price, gs.config.Risk.StopLoss.PriceTrigger)
		}
		
		if gs.config.Risk.StopLoss.PnLTrigger != 0 && gs.statistics.NetPnL <= gs.config.Risk.StopLoss.PnLTrigger {
			return fmt.Errorf("stop loss PnL trigger activated: PnL %.2f <= trigger %.2f", 
				gs.statistics.NetPnL, gs.config.Risk.StopLoss.PnLTrigger)
		}
	}
	
	// Check take profit triggers
	if gs.config.Risk.TakeProfit.Enabled {
		if gs.config.Risk.TakeProfit.PriceTrigger > 0 && condition.Price >= gs.config.Risk.TakeProfit.PriceTrigger {
			return fmt.Errorf("take profit price trigger activated: price %.2f >= trigger %.2f", 
				condition.Price, gs.config.Risk.TakeProfit.PriceTrigger)
		}
		
		if gs.config.Risk.TakeProfit.PnLTrigger > 0 && gs.statistics.NetPnL >= gs.config.Risk.TakeProfit.PnLTrigger {
			return fmt.Errorf("take profit PnL trigger activated: PnL %.2f >= trigger %.2f", 
				gs.statistics.NetPnL, gs.config.Risk.TakeProfit.PnLTrigger)
		}
		
		if gs.config.Risk.TakeProfit.ROIPercentage > 0 && gs.statistics.ROI >= gs.config.Risk.TakeProfit.ROIPercentage {
			return fmt.Errorf("take profit ROI trigger activated: ROI %.2f%% >= trigger %.2f%%", 
				gs.statistics.ROI, gs.config.Risk.TakeProfit.ROIPercentage)
		}
	}
	
	// Check maximum drawdown
	if gs.statistics.CurrentDrawdown >= gs.config.Risk.MaxDrawdown {
		return fmt.Errorf("maximum drawdown exceeded: %.2f%% >= %.2f%%", 
			gs.statistics.CurrentDrawdown, gs.config.Risk.MaxDrawdown)
	}
	
	return nil
}
