package strategy

import (
	"time"

	"github.com/adshao/go-binance/v2/futures"
	"github.com/adshao/go-binance/v2/futures/gridbot/config"
)

// GridStrategy represents the main grid trading strategy
type GridStrategy struct {
	Config     *config.Config
	Levels     []*GridLevel
	Statistics *StrategyStatistics
	Status     StrategyStatus
	CreatedAt  time.Time
	UpdatedAt  time.Time
}

// GridLevel represents a single level in the grid
type GridLevel struct {
	ID        int                     `json:"id"`
	Price     float64                 `json:"price"`
	Side      futures.SideType        `json:"side"`
	Quantity  float64                 `json:"quantity"`
	OrderID   int64                   `json:"order_id,omitempty"`
	Status    config.GridLevelStatus  `json:"status"`
	CreatedAt time.Time               `json:"created_at"`
	UpdatedAt time.Time               `json:"updated_at"`
	FilledAt  *time.Time              `json:"filled_at,omitempty"`
	
	// Order details
	OriginalQuantity string `json:"original_quantity,omitempty"`
	ExecutedQuantity string `json:"executed_quantity,omitempty"`
	CumulativeQuote  string `json:"cumulative_quote,omitempty"`
	Commission       string `json:"commission,omitempty"`
	CommissionAsset  string `json:"commission_asset,omitempty"`
}

// StrategyStatus represents the current status of the strategy
type StrategyStatus string

const (
	StrategyStatusInitializing StrategyStatus = "initializing"
	StrategyStatusActive       StrategyStatus = "active"
	StrategyStatusPaused       StrategyStatus = "paused"
	StrategyStatusStopped      StrategyStatus = "stopped"
	StrategyStatusError        StrategyStatus = "error"
	StrategyStatusCompleted    StrategyStatus = "completed"
)

// StrategyStatistics contains performance metrics for the strategy
type StrategyStatistics struct {
	StartTime         time.Time `json:"start_time"`
	EndTime           *time.Time `json:"end_time,omitempty"`
	RunDuration       time.Duration `json:"run_duration"`
	
	// Trading statistics
	TotalTrades       int     `json:"total_trades"`
	BuyTrades         int     `json:"buy_trades"`
	SellTrades        int     `json:"sell_trades"`
	ProfitableTrades  int     `json:"profitable_trades"`
	LosingTrades      int     `json:"losing_trades"`
	
	// Financial statistics
	TotalVolume       float64 `json:"total_volume"`
	TotalPnL          float64 `json:"total_pnl"`
	RealizedPnL       float64 `json:"realized_pnl"`
	UnrealizedPnL     float64 `json:"unrealized_pnl"`
	TotalFees         float64 `json:"total_fees"`
	NetPnL            float64 `json:"net_pnl"`
	ROI               float64 `json:"roi"`
	
	// Risk statistics
	MaxDrawdown       float64 `json:"max_drawdown"`
	MaxDrawdownDate   *time.Time `json:"max_drawdown_date,omitempty"`
	CurrentDrawdown   float64 `json:"current_drawdown"`
	WinRate           float64 `json:"win_rate"`
	ProfitFactor      float64 `json:"profit_factor"`
	SharpeRatio       float64 `json:"sharpe_ratio"`
	
	// Grid statistics
	GridEfficiency    float64 `json:"grid_efficiency"`
	ActiveGrids       int     `json:"active_grids"`
	FilledGrids       int     `json:"filled_grids"`
	AvgTradeSize      float64 `json:"avg_trade_size"`
	AvgHoldTime       time.Duration `json:"avg_hold_time"`
	
	// Price statistics
	HighestPrice      float64 `json:"highest_price"`
	LowestPrice       float64 `json:"lowest_price"`
	CurrentPrice      float64 `json:"current_price"`
	PriceRange        float64 `json:"price_range"`
	
	// Performance metrics
	TradesPerHour     float64 `json:"trades_per_hour"`
	VolumePerHour     float64 `json:"volume_per_hour"`
	LastTradeTime     *time.Time `json:"last_trade_time,omitempty"`
}

// GridCalculationResult contains the result of grid calculation
type GridCalculationResult struct {
	Levels        []*GridLevel `json:"levels"`
	TotalQuantity float64      `json:"total_quantity"`
	RequiredMargin float64     `json:"required_margin"`
	GridSpacing   float64      `json:"grid_spacing"`
	PriceRange    float64      `json:"price_range"`
}

// OrderPlacementRequest represents a request to place an order
type OrderPlacementRequest struct {
	GridLevel    *GridLevel
	Symbol       string
	Side         futures.SideType
	OrderType    futures.OrderType
	Quantity     string
	Price        string
	TimeInForce  futures.TimeInForceType
	PositionSide futures.PositionSideType
	ReduceOnly   bool
}

// OrderPlacementResult represents the result of placing an order
type OrderPlacementResult struct {
	Success   bool
	OrderID   int64
	Error     error
	GridLevel *GridLevel
	Response  *futures.CreateOrderResponse
}

// GridRebalanceRequest represents a request to rebalance the grid
type GridRebalanceRequest struct {
	CurrentPrice  float64
	ForceRebalance bool
	Reason        string
}

// GridRebalanceResult represents the result of grid rebalancing
type GridRebalanceResult struct {
	Success         bool
	OrdersPlaced    int
	OrdersCancelled int
	NewLevels       []*GridLevel
	Error           error
	Details         string
}

// MarketCondition represents current market conditions
type MarketCondition struct {
	Price         float64   `json:"price"`
	Timestamp     time.Time `json:"timestamp"`
	Volume24h     float64   `json:"volume_24h"`
	PriceChange24h float64  `json:"price_change_24h"`
	Volatility    float64   `json:"volatility"`
	Trend         TrendDirection `json:"trend"`
	
	// Order book data
	BidPrice      float64   `json:"bid_price"`
	AskPrice      float64   `json:"ask_price"`
	Spread        float64   `json:"spread"`
	SpreadPercent float64   `json:"spread_percent"`
}

// TrendDirection represents market trend direction
type TrendDirection string

const (
	TrendUp      TrendDirection = "up"
	TrendDown    TrendDirection = "down"
	TrendSideways TrendDirection = "sideways"
	TrendUnknown  TrendDirection = "unknown"
)

// GridEvent represents events that occur during grid trading
type GridEvent struct {
	Type      GridEventType `json:"type"`
	Timestamp time.Time     `json:"timestamp"`
	GridLevel *GridLevel    `json:"grid_level,omitempty"`
	Price     float64       `json:"price,omitempty"`
	Message   string        `json:"message"`
	Data      interface{}   `json:"data,omitempty"`
}

// GridEventType represents different types of grid events
type GridEventType string

const (
	GridEventOrderPlaced    GridEventType = "order_placed"
	GridEventOrderFilled    GridEventType = "order_filled"
	GridEventOrderCancelled GridEventType = "order_cancelled"
	GridEventOrderError     GridEventType = "order_error"
	GridEventGridRebalanced GridEventType = "grid_rebalanced"
	GridEventPriceOutOfRange GridEventType = "price_out_of_range"
	GridEventRiskTriggered  GridEventType = "risk_triggered"
	GridEventStrategyPaused GridEventType = "strategy_paused"
	GridEventStrategyResumed GridEventType = "strategy_resumed"
	GridEventStrategyStopped GridEventType = "strategy_stopped"
)

// StrategyInterface defines the interface for grid strategies
type StrategyInterface interface {
	// Initialize the strategy
	Initialize() error
	
	// Start the strategy
	Start() error
	
	// Stop the strategy
	Stop() error
	
	// Pause the strategy
	Pause() error
	
	// Resume the strategy
	Resume() error
	
	// Update strategy with new market data
	Update(condition *MarketCondition) error
	
	// Get current strategy status
	GetStatus() StrategyStatus
	
	// Get strategy statistics
	GetStatistics() *StrategyStatistics
	
	// Get grid levels
	GetGridLevels() []*GridLevel
	
	// Rebalance grid
	Rebalance(request *GridRebalanceRequest) (*GridRebalanceResult, error)
	
	// Handle order fill event
	HandleOrderFill(orderID int64, fillPrice float64, fillQuantity float64) error
	
	// Calculate required margin
	CalculateRequiredMargin() (float64, error)
	
	// Validate strategy configuration
	Validate() error
}
